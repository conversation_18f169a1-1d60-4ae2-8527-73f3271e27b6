import { isEmpty } from 'lodash';
import React, { FC, MutableRefObject, useCallback, useMemo } from 'react';
import classNames from 'classnames';
import { ED_COM_CHAT_ATTACHMENTS } from '../../../../../fsCategories';
import { IFileAttachmentTemp } from '../../../../abstract/IFileAttachments';
import { useDependsOnFields } from '../../../containers/EntityForm';
import EntityFormFieldSet from '../../../containers/EntityForm/EntityFormFieldSet';
import AttachmentField from '../../../containers/EntityForm/fields/AttachmentField';
import EmojiField from '../../../containers/EntityForm/fields/EmojiField';
import TextAreaField from '../../../containers/EntityForm/fields/TextAreaField';
import useEntityFormContext from '../../../containers/EntityForm/internal/useEntityFormContext';
import IconButton from '../../../controls/IconButton';
import useT from '../../../utils/Translations/useT';
import useAudioRecord from '../hooks/useAudioRecord';
import styles from './MessagesItemsCreationBody.scss';
import ReplyMessageView from './ReplyMessageView';
import useStorageSettings from '../../../controls/base/TextEditor/useStorageSettings';
import useCurrentUser from '../../../../data/hooks/useCurrentUser';

const MessagesItemsCreationBody: FC<IMessagesItemsCreationBodyProps> = ({
  textAreaRef,
}) => {
  const t = useT();
  const {
    me: { tenantId },
  } = useCurrentUser();
  const { content, attachments } = useDependsOnFields<{
    content: string;
    attachments: IFileAttachmentTemp[];
  }>({
    content: 'content',
    attachments: 'attachments',
  });
  const { maxSize } = useStorageSettings(ED_COM_CHAT_ATTACHMENTS, tenantId);

  const { submitForm, resetForm, isSubmitting } = useEntityFormContext();

  const hasSendButton = useMemo<boolean>(
    () => !isEmpty(content) || !isEmpty(attachments),
    [content, attachments],
  );

  const handleButtonClick = useCallback(async () => {
    if (hasSendButton) {
      await submitForm();
      await resetForm();
    }
  }, [hasSendButton, submitForm, resetForm]);

  const { button, hasAudioMessage, visualizer } = useAudioRecord({
    categoryKey: ED_COM_CHAT_ATTACHMENTS,
    name: 'voiceMessage',
  });

  const isDisabled = useMemo<boolean>(() => isSubmitting || hasAudioMessage, [
    isSubmitting,
    hasAudioMessage,
  ]);

  return (
    <div className={styles.visualWrapper}>
      <ReplyMessageView />
      <EntityFormFieldSet className={styles.inputWrapper}>
        <EmojiField hasAudioMessage={hasAudioMessage} isDisabled={isDisabled} />
        <div className={styles.textArea}>
          {hasAudioMessage ? (
            visualizer
          ) : (
            <TextAreaField
              autoHeight
              hasSubmitOnEnter
              noLabel
              columns={1}
              inputProps={{
                autoFocus: true,
                ref: textAreaRef,
              }}
              label={t('Type a message')}
              maxLength={4000}
              name="content"
              rows={1}
            />
          )}
        </div>

        {hasSendButton ? (
          <IconButton
            inline
            className={styles.send}
            iconName="paperplane"
            isDisable={isDisabled}
            isLoading={isDisabled}
            title={t('Send message')}
            onClick={isDisabled ? undefined : handleButtonClick}
          />
        ) : (
          button
        )}
      </EntityFormFieldSet>

      <EntityFormFieldSet>
        <AttachmentField
          noWrapperStyle
          addButtonTitle={t('Attach File')}
          categoryKey={ED_COM_CHAT_ATTACHMENTS}
          columns={1}
          hasRoutes={false}
          isDisabled={isDisabled}
          isEditable={!isDisabled}
          maxSize={maxSize}
          name="attachments"
          wrapperClassNames={{
            1: isDisabled
              ? classNames(
                  `col-lg-12 col-md-12 col-sm-12 col-xs-12`,
                  styles.cursorNotAllowed,
                )
              : 'col-lg-12 col-md-12 col-sm-12 col-xs-12',
          }}
        />
      </EntityFormFieldSet>
    </div>
  );
};

export default MessagesItemsCreationBody;

interface IMessagesItemsCreationBodyProps {
  textAreaRef: MutableRefObject<HTMLTextAreaElement | undefined>;
}
